# 订单管理重构：从课程 SKU 到商品 SKU

## 重构背景

根据课程与商品分离的架构调整，订单管理系统需要从关联 `edusys_wbclass_course_sku` 改为关联 `edusys_wbclass_course_product_sku`。

## 主要变更

### 1. 前端界面重构 (`yudao-ui-admin/src/views/wbclass/wbclassCourseOrder/index.vue`)

#### 字段名称变更

- `courseId` → `productId` (查询条件中)
- "课程 ID" → "商品 ID" (表单标签)
- "课程信息" → "商品信息" (表格列标题)
- "课程名称" → "商品名称" (详情页面)

#### 数据源变更

- `courseOptions` → `productOptions`
- `assignSelectedCourseSkus` → `assignSelectedProductSkus`
- `loadCourseOptions()` → `loadProductOptions()`
- `handleAssignCourseChange()` → `handleAssignProductChange()`

#### 导入模块变更

```javascript
// 变更前
import { getWbclassCoursePage } from "@/api/wbclass/wbclassCourse";

// 变更后
import { getWbclassCourseProductPage } from "@/api/wbclass/courseProduct";
```

#### 手动分配订单逻辑变更

- 从选择课程 → 选择商品
- 从课程 SKU → 商品 SKU
- 价格信息展示逻辑相应调整

### 2. 后端接口支持

#### 需要确保后端接口支持的字段

- `productId`: 商品 ID
- `skuId`: 商品 SKU ID
- `productName`: 商品名称快照
- `skuName`: SKU 名称快照
- `skuCode`: SKU 编码快照

#### 订单表数据库结构

```sql
-- 已存在的字段
ALTER TABLE `edusys_wbclass_course_order`
ADD COLUMN `product_id` bigint COMMENT '商品ID',
ADD COLUMN `sku_id` bigint COMMENT 'SKU ID',
ADD KEY `idx_product_id` (`product_id`),
ADD KEY `idx_sku_id` (`sku_id`);
```

### 3. 兼容性处理

#### 显示逻辑兼容

```javascript
// 商品名称显示优先级: productName > courseName
<div>{{ scope.row.productName || scope.row.courseName }}</div>
```

#### 查询参数兼容

- 保留原有的 `courseId` 查询逻辑
- 新增 `productId` 查询支持

## 实施步骤

1. ✅ 前端界面字段名称调整
2. ✅ 前端数据源和方法名调整
3. ✅ 前端导入模块更新
4. ✅ 手动分配订单逻辑重构
5. ⏳ 后端接口验证和调整
6. ⏳ 数据迁移脚本验证
7. ⏳ 前后端联调测试

## 注意事项

1. **数据兼容性**: 需要确保历史订单数据能正常显示
2. **接口兼容性**: 后端接口需要同时支持新旧字段
3. **用户体验**: 界面调整后功能保持一致
4. **数据完整性**: 商品 SKU 关联的课程信息需要正确维护

## 测试验证

### 功能测试点

- [ ] 订单列表查询展示
- [ ] 订单详情查看
- [ ] 手动分配订单功能
- [ ] 订单取消功能
- [ ] 订单退款功能
- [ ] 导出功能

### 数据验证点

- [ ] 商品信息正确显示
- [ ] SKU 信息正确关联
- [ ] 价格信息准确展示
- [ ] 历史订单兼容显示

## 文件变更清单

### 前端文件

- `yudao-ui-admin/src/views/wbclass/wbclassCourseOrder/index.vue` ✅

### 后端文件 (待确认)

- 订单相关 Service 实现
- 订单相关 Controller
- 订单相关 Mapper
- 相关 VO 对象

### 数据库脚本

- 已有课程产品分离相关脚本
- 订单表结构升级脚本
