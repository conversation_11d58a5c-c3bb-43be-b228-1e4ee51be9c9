-- 优化课程订单表结构
-- 确保 product_id 字段有合适的约束和索引

-- 1. 为 product_id 字段添加索引和约束
ALTER TABLE edusys_wbclass_course_order 
ADD INDEX idx_product_id (product_id),
ADD INDEX idx_sku_id (sku_id),
ADD INDEX idx_product_sku (product_id, sku_id);

-- 2. 修改字段注释，明确字段用途
ALTER TABLE edusys_wbclass_course_order 
MODIFY COLUMN product_id bigint COMMENT '产品ID，关联 edusys_wbclass_course_product.id',
MODIFY COLUMN sku_id bigint COMMENT 'SKU ID，关联 edusys_wbclass_course_product_sku.id',
MODIFY COLUMN course_id bigint NOT NULL COMMENT '主课程ID，关联 edusys_wbclass_course.id（用于向下兼容）';

-- 3. 添加外键约束（可选，根据实际需要决定是否启用）
-- ALTER TABLE edusys_wbclass_course_order 
-- ADD CONSTRAINT fk_order_product FOREIGN KEY (product_id) REFERENCES edusys_wbclass_course_product(id),
-- ADD CONSTRAINT fk_order_sku FOREIGN KEY (sku_id) REFERENCES edusys_wbclass_course_product_sku(id);

-- 4. 创建复合索引优化查询性能
ALTER TABLE edusys_wbclass_course_order 
ADD INDEX idx_user_status (user_id, status),
ADD INDEX idx_user_pay_status (user_id, pay_status),
ADD INDEX idx_order_time (create_time, status);

-- 5. 数据一致性检查查询（用于验证修改后的数据）
-- 检查订单表中的数据完整性
SELECT 
    '订单数据统计' as check_type,
    COUNT(*) as total_orders,
    COUNT(product_id) as orders_with_product,
    COUNT(sku_id) as orders_with_sku,
    COUNT(CASE WHEN product_id IS NULL AND sku_id IS NOT NULL THEN 1 END) as missing_product_id,
    COUNT(CASE WHEN product_id IS NOT NULL AND sku_id IS NULL THEN 1 END) as missing_sku_id
FROM edusys_wbclass_course_order;

-- 检查 SKU 和 Product 的关联关系
SELECT 
    '关联关系检查' as check_type,
    COUNT(*) as total_sku_records,
    COUNT(DISTINCT product_id) as distinct_products,
    MIN(product_id) as min_product_id,
    MAX(product_id) as max_product_id
FROM edusys_wbclass_course_product_sku;
