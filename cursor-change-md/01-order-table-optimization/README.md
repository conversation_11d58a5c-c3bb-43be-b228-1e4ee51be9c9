# 课程订单表结构优化

## 背景

原有的课程订单表 `edusys_wbclass_course_order` 存在以下问题：

1. **数据关联不完整**：订单表中有 `sku_id` 字段但缺少 `product_id` 字段
2. **架构不一致**：新系统采用 Product -> SKU -> Course 的架构，但订单表没有正确关联 Product
3. **数据完整性问题**：部分订单记录的 `sku_id` 为 null，但有 `sku_name`
4. **查询效率低**：缺少必要的索引

## 解决方案

### 1. 数据库表结构优化

- 为 `product_id` 字段添加索引和约束
- 添加复合索引优化查询性能
- 明确字段注释和用途

### 2. 代码层面修改

- **实体类**：在 `WbClassCourseOrderDO` 中添加 `productId` 字段
- **Mapper**：在查询条件中支持 `productId` 过滤
- **服务层**：在创建订单时正确设置 `productId`
- **VO类**：在请求和响应 VO 中添加 `productId` 字段

### 3. 业务逻辑优化

- 创建订单时，通过 `skuId` 获取对应的 `productId`
- 确保订单数据的完整性和一致性
- 支持通过 `productId` 进行订单查询和统计

## 修改文件清单

### 数据库
- `order_table_structure_fix.sql` - 数据库表结构优化脚本

### 实体类
- `WbClassCourseOrderDO.java` - 添加 productId 字段

### Mapper
- `WbClassCourseOrderMapper.java` - 添加 productId 查询条件

### 服务层
- `WbClassCourseOrderServiceImpl.java` - 创建订单时设置 productId

### VO类
- `WbClassCourseOrderPageReqVO.java` - 添加 productId 查询参数
- `WbClassCourseOrderRespVO.java` - 添加 productId 响应字段
- `AppWbClassCourseOrderRespVO.java` - 添加 productId 和 SKU 相关字段

## 数据架构说明

### 新架构关系
```
Product (产品) 
  ↓ 1:N
SKU (规格) 
  ↓ N:M  
Course (课程)
  ↓ 1:N
Order (订单)
```

### 字段关系
- `product_id`: 关联 `edusys_wbclass_course_product.id`
- `sku_id`: 关联 `edusys_wbclass_course_product_sku.id`
- `course_id`: 关联 `edusys_wbclass_course.id` (主课程，用于向下兼容)

## 执行步骤

1. **执行数据库脚本**
   ```sql
   -- 执行 order_table_structure_fix.sql
   ```

2. **部署代码更新**
   - 重新编译和部署应用

3. **数据验证**
   - 检查新创建的订单是否正确设置了 productId
   - 验证查询功能是否正常工作

## 注意事项

1. **向下兼容**：保留了原有的 `course_id` 字段，确保现有功能不受影响
2. **数据完整性**：新订单会自动设置 `product_id`，历史数据保持不变
3. **性能优化**：添加了必要的索引，提升查询性能
4. **业务逻辑**：手动分配订单时需要同时提供 `productId` 和 `skuId`

## 验证方法

### 1. 数据库验证
```sql
-- 检查订单数据完整性
SELECT 
    COUNT(*) as total_orders,
    COUNT(product_id) as orders_with_product,
    COUNT(sku_id) as orders_with_sku
FROM edusys_wbclass_course_order;

-- 检查新订单是否正确设置了 product_id
SELECT * FROM edusys_wbclass_course_order 
WHERE create_time > NOW() - INTERVAL 1 DAY
ORDER BY id DESC LIMIT 10;
```

### 2. 功能验证
- 创建新订单，验证 `product_id` 是否正确设置
- 测试订单查询功能，验证 `productId` 过滤是否生效
- 测试手动分配订单功能

## 后续优化建议

1. **数据清理**：可以考虑为历史订单补充 `product_id` 数据
2. **监控告警**：添加数据完整性监控，确保新订单数据质量
3. **性能优化**：根据实际查询模式，进一步优化索引策略
