# 订单管理重构 - 从课程 SKU 到商品 SKU

## 重构概述

本次重构将订单管理系统从关联课程 SKU 改为关联商品 SKU，以支持更灵活的商品管理模式。

## 数据结构变化

### 新的数据表结构

- `edusys_wbclass_course_product` - 课程商品表
- `edusys_wbclass_course_product_sku` - 课程商品 SKU 表
- `edusys_wbclass_sku_course_relation` - SKU 和课程的关联表

### 关联关系变化

- **之前**: 订单 -> 课程 -> 课程 SKU
- **现在**: 订单 -> 商品 SKU -> 课程（通过关联表）

## 后端修改

### 1. 错误常量添加

在 `ErrorCodeConstants.java` 中添加了新的错误码：

- `PRODUCT_SKU_NOT_EXISTS` - 商品 SKU 不存在
- `PRODUCT_SKU_NOT_MATCH_PRODUCT` - 商品 SKU 与商品不匹配
- `SKU_NO_COURSE_RELATION` - SKU 未关联任何课程
- `PRODUCT_SKU_ALREADY_PURCHASED` - 商品 SKU 已购买

### 2. 订单分配 VO 修改

`WbClassCourseOrderAssignReqVO.java`:

- 将 `courseId` 改为 `productId`
- `skuId` 现在指向商品 SKU 而非课程 SKU
- 添加了必填校验

### 3. 订单服务逻辑重构

`WbClassCourseOrderServiceImpl.java` 的 `assignOrder` 方法：

- 校验商品 SKU 存在性
- 校验商品 ID 与 SKU 的匹配关系
- 获取 SKU 关联的课程列表
- 使用商品 SKU 的价格信息
- 增加 SKU 销量统计

## 前端修改

### 1. API 导入

添加了商品相关 API 的导入：

```javascript
import {
  getWbclassCourseProductPage,
  getWbclassCourseProductSkuList,
} from "@/api/wbclass/courseProduct";
```

### 2. 数据结构调整

- 添加 `productOptions` - 商品选项列表
- 将 `assignSelectedCourseSkus` 改为 `assignSelectedProductSkus`
- 修改表单校验规则

### 3. 界面修改

手动分配订单对话框：

- "课程" 选择改为 "商品" 选择
- "SKU" 改为 "商品 SKU"
- 调整事件处理方法

### 4. 方法重构

- `handleAssignCourseChange` 改为 `handleAssignProductChange`
- 新增 `loadProductOptions` 方法
- 修改 `handleAssignSkuChange` 逻辑

## 测试要点

### 1. 后端测试

#### 手动分配订单测试

- [ ] 验证商品 SKU 校验逻辑
- [ ] 测试商品 ID 与 SKU 匹配校验
- [ ] 确认 SKU 课程关联查询正常
- [ ] 验证价格信息正确获取
- [ ] 测试重复购买检查

#### App 端订单创建测试

- [ ] 测试只传 courseId 的传统模式
- [ ] 测试只传 skuId 的新模式
- [ ] 测试同时传 courseId 和 skuId
- [ ] 测试都不传的错误情况
- [ ] 验证 SKU 价格信息正确使用

#### 管理端订单创建测试

- [ ] 测试只传 courseId 的传统模式
- [ ] 测试只传 skuId 的新模式
- [ ] 验证参数校验逻辑

#### 支付成功处理测试

- [ ] 验证 SKU 销量正确增加
- [ ] 确认学习进度正确初始化

### 2. 前端测试

- [ ] 商品列表正常加载
- [ ] 选择商品后 SKU 列表正确显示
- [ ] 选择 SKU 后价格信息自动填充
- [ ] 表单校验规则正常工作
- [ ] 订单分配成功提交

### 3. 集成测试

- [ ] 完整的手动分配订单流程
- [ ] App 端创建订单 -> 支付成功流程
- [ ] 管理端创建订单流程
- [ ] 订单数据正确保存
- [ ] 学习进度正确初始化
- [ ] SKU 销量正确增加

### 4. 兼容性测试

- [ ] 现有订单数据正常显示
- [ ] 旧的课程订单功能不受影响
- [ ] API 接口向后兼容

## 注意事项

1. **数据兼容性**: 现有订单数据需要保持兼容
2. **权限控制**: 确保 API 权限配置正确
3. **错误处理**: 新增的错误码需要前端正确处理
4. **性能考虑**: SKU 课程关联查询的性能影响

## 详细修改清单

### 后端文件修改

1. **WbClassCourseOrderServiceImpl.java**

   - 修改 `assignOrder` 方法：支持商品 SKU 逻辑
   - 修改 `createOrder` 方法：支持商品 SKU 和传统课程两种模式
   - 修改 `createOrderByAdmin` 方法：支持商品 SKU 和传统课程两种模式
   - 修改 `payOrderSuccess` 方法：支持 SKU 销量统计
   - 新增 `hasUserPurchasedProductSku` 方法

2. **WbClassCourseOrderAssignReqVO.java**

   - 将 `courseId` 改为 `productId`
   - 增强 `skuId` 的必填校验

3. **AppWbClassCourseOrderCreateReqVO.java**

   - 更新 `courseId` 和 `skuId` 字段注释
   - 移除 `courseId` 的必填校验

4. **WbClassCourseOrderCreateReqVO.java**

   - 更新 `courseId` 和 `skuId` 字段注释
   - 移除 `courseId` 的必填校验

5. **ErrorCodeConstants.java**
   - 新增商品 SKU 相关错误码

### 前端文件修改

1. **index.vue**
   - 添加商品相关 API 导入
   - 新增 `productOptions` 和 `assignSelectedProductSkus` 数据
   - 修改表单校验规则
   - 新增 `loadProductOptions` 方法
   - 修改 `handleAssignProductChange` 和 `handleAssignSkuChange` 方法
   - 更新手动分配订单对话框 HTML

## 后续优化建议

1. 考虑添加商品 SKU 缓存机制
2. 优化 SKU 课程关联查询性能
3. 添加批量操作支持
4. 完善订单状态流转逻辑
5. 考虑添加商品 SKU 库存管理
6. 优化前端商品选择体验
