-- 订单管理重构 - 从课程SKU到商品SKU
-- 数据库迁移脚本

-- 1. 检查现有订单数据中的SKU使用情况
SELECT 
    COUNT(*) as total_orders,
    COUNT(sku_id) as orders_with_sku,
    COUNT(CASE WHEN sku_id IS NULL THEN 1 END) as orders_without_sku
FROM edusys_wbclass_course_order;

-- 2. 检查现有SKU数据的分布
SELECT 
    'course_sku' as sku_type,
    COUNT(*) as count
FROM edusys_wbclass_course_sku
WHERE deleted = 0
UNION ALL
SELECT 
    'product_sku' as sku_type,
    COUNT(*) as count
FROM edusys_wbclass_course_product_sku
WHERE deleted = 0;

-- 3. 检查商品SKU与课程的关联关系
SELECT 
    ps.id as product_sku_id,
    ps.sku_name,
    ps.product_id,
    COUNT(scr.course_id) as related_courses
FROM edusys_wbclass_course_product_sku ps
LEFT JOIN edusys_wbclass_sku_course_relation scr ON ps.id = scr.sku_id
WHERE ps.deleted = 0
GROUP BY ps.id, ps.sku_name, ps.product_id
ORDER BY ps.id;

-- 4. 检查是否有订单使用了不存在的SKU ID
SELECT 
    o.id as order_id,
    o.order_no,
    o.sku_id,
    o.sku_name,
    CASE 
        WHEN ps.id IS NOT NULL THEN 'product_sku_exists'
        WHEN cs.id IS NOT NULL THEN 'course_sku_exists'
        ELSE 'sku_not_found'
    END as sku_status
FROM edusys_wbclass_course_order o
LEFT JOIN edusys_wbclass_course_product_sku ps ON o.sku_id = ps.id AND ps.deleted = 0
LEFT JOIN edusys_wbclass_course_sku cs ON o.sku_id = cs.id AND cs.deleted = 0
WHERE o.sku_id IS NOT NULL
ORDER BY o.id;

-- 5. 数据迁移建议（如果需要）
-- 注意：以下脚本仅为示例，实际执行前需要根据具体情况调整

-- 如果需要将现有的课程SKU订单迁移到商品SKU
-- 这个脚本需要根据实际的数据映射关系来编写
/*
-- 示例：假设需要创建默认商品和SKU来兼容现有订单
INSERT INTO edusys_wbclass_course_product (name, description, status, creator, create_time, updater, update_time, deleted)
SELECT 
    CONCAT('默认商品-', c.name) as name,
    CONCAT('为课程 ', c.name, ' 自动创建的默认商品') as description,
    1 as status,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    0 as deleted
FROM edusys_wbclass_course c
WHERE c.deleted = 0
AND NOT EXISTS (
    SELECT 1 FROM edusys_wbclass_course_product p 
    WHERE p.name = CONCAT('默认商品-', c.name) AND p.deleted = 0
);
*/

-- 6. 验证重构后的数据完整性
-- 检查所有商品SKU都有关联的课程
SELECT 
    ps.id,
    ps.sku_name,
    ps.product_id,
    CASE 
        WHEN COUNT(scr.course_id) = 0 THEN 'NO_COURSE_RELATION'
        ELSE 'HAS_COURSE_RELATION'
    END as relation_status
FROM edusys_wbclass_course_product_sku ps
LEFT JOIN edusys_wbclass_sku_course_relation scr ON ps.id = scr.sku_id
WHERE ps.deleted = 0
GROUP BY ps.id, ps.sku_name, ps.product_id
HAVING COUNT(scr.course_id) = 0;

-- 7. 性能优化建议
-- 为订单表的sku_id字段添加索引（如果还没有）
-- ALTER TABLE edusys_wbclass_course_order ADD INDEX idx_sku_id (sku_id);

-- 为SKU课程关联表添加索引（如果还没有）
-- ALTER TABLE edusys_wbclass_sku_course_relation ADD INDEX idx_sku_id (sku_id);
-- ALTER TABLE edusys_wbclass_sku_course_relation ADD INDEX idx_course_id (course_id);

-- 8. 数据清理建议
-- 清理无效的订单数据（如果有）
-- DELETE FROM edusys_wbclass_course_order 
-- WHERE sku_id IS NOT NULL 
-- AND sku_id NOT IN (
--     SELECT id FROM edusys_wbclass_course_product_sku WHERE deleted = 0
-- );

-- 9. 监控查询
-- 监控重构后的订单创建情况
SELECT 
    DATE(create_time) as order_date,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN sku_id IS NOT NULL THEN 1 END) as orders_with_sku,
    COUNT(CASE WHEN sku_id IS NULL THEN 1 END) as orders_without_sku
FROM edusys_wbclass_course_order
WHERE create_time >= CURDATE() - INTERVAL 7 DAY
GROUP BY DATE(create_time)
ORDER BY order_date DESC;

-- 10. 回滚计划
-- 如果重构出现问题，需要准备回滚方案
-- 建议在重构前备份相关表：
-- CREATE TABLE edusys_wbclass_course_order_backup AS SELECT * FROM edusys_wbclass_course_order;
