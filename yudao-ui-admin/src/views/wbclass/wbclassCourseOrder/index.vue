<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程ID" prop="courseId">
        <el-input
          v-model="queryParams.courseId"
          placeholder="请输入课程ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SKU名称" prop="skuName">
        <el-input
          v-model="queryParams.skuName"
          placeholder="请输入SKU名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择订单状态"
          clearable
        >
          <el-option
            v-for="dict in orderStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select
          v-model="queryParams.payStatus"
          placeholder="请选择支付状态"
          clearable
        >
          <el-option
            v-for="dict in payStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['wbclass:course-order:export']"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-user"
          size="mini"
          @click="handleAssign"
          v-hasPermi="['wbclass:course-order:assign']"
        >
          手动分配订单
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="订单号"
        align="center"
        prop="orderNo"
        width="180"
      />
      <el-table-column
        label="用户ID"
        align="center"
        prop="userId"
        width="100"
      />
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.extraUser">
            <div>{{ scope.row.extraUser.nickname }}</div>
            <div style="color: #999; font-size: 12px">
              {{ scope.row.extraUser.mobile }}
            </div>
            <div style="color: #999; font-size: 12px">
              id：{{ scope.row.extraUser.id }}
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="课程信息" align="center" width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.courseName }}</div>
          <div v-if="scope.row.skuName" style="color: #666; font-size: 12px">
            SKU: {{ scope.row.skuName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="价格信息" align="center" width="150">
        <template slot-scope="scope">
          <div>原价: ¥{{ formatPrice(scope.row.originalPrice) }}</div>
          <div
            v-if="scope.row.discountPrice > 0"
            style="color: #f56c6c; font-size: 12px"
          >
            优惠: -¥{{ formatPrice(scope.row.discountPrice) }}
          </div>
          <div style="color: #67c23a; font-weight: bold">
            实付: ¥{{ formatPrice(scope.row.payPrice) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getOrderStatusTagType(scope.row.status)" size="mini">
            {{ getOrderStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getPayStatusTagType(scope.row.payStatus)" size="mini">
            {{ getPayStatusText(scope.row.payStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="支付时间"
        align="center"
        prop="payTime"
        width="180"
        :formatter="parseTimeFormatter"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
        :formatter="parseTimeFormatter"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['wbclass:course-order:query']"
          >
            查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleCancel(scope.row)"
            v-if="scope.row.status === 1"
            v-hasPermi="['wbclass:course-order:cancel']"
          >
            取消
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh-right"
            @click="handleRefund(scope.row)"
            v-if="scope.row.status === 2"
            v-hasPermi="['wbclass:course-order:refund']"
          >
            退款
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog
      title="订单详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{
          viewData.orderNo
        }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{
          viewData.userId
        }}</el-descriptions-item>
        <el-descriptions-item label="用户信息">
          <div v-if="viewData.extraUser">
            <div>
              {{ viewData.extraUser.nickname }} ({{
                viewData.extraUser.mobile
              }})
            </div>
          </div>
          <div v-else>用户信息未找到</div>
        </el-descriptions-item>
        <el-descriptions-item label="下单终端">
          {{ getTerminalText(viewData.terminal) }}
        </el-descriptions-item>
        <el-descriptions-item label="课程名称">{{
          viewData.courseName
        }}</el-descriptions-item>
        <el-descriptions-item label="SKU信息">
          <div v-if="viewData.skuName">
            <div>{{ viewData.skuName }}</div>
            <div style="color: #999; font-size: 12px">
              {{ viewData.skuCode }}
            </div>
          </div>
          <div v-else>无SKU（整个课程）</div>
        </el-descriptions-item>
        <el-descriptions-item label="原价"
          >¥{{ formatPrice(viewData.originalPrice) }}</el-descriptions-item
        >
        <el-descriptions-item label="优惠金额"
          >¥{{ formatPrice(viewData.discountPrice) }}</el-descriptions-item
        >
        <el-descriptions-item label="实付金额"
          >¥{{ formatPrice(viewData.payPrice) }}</el-descriptions-item
        >
        <el-descriptions-item label="订单状态">
          <el-tag :type="getOrderStatusTagType(viewData.status)">
            {{ getOrderStatusText(viewData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag :type="getPayStatusTagType(viewData.payStatus)">
            {{ getPayStatusText(viewData.payStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付时间">
          {{ parseTime(viewData.payTime) || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="支付渠道">
          {{ getPayChannelText(viewData.payChannel) }}
        </el-descriptions-item>
        <el-descriptions-item label="退款金额"
          >¥{{ formatPrice(viewData.refundPrice) }}</el-descriptions-item
        >
        <el-descriptions-item label="退款时间">
          {{ parseTime(viewData.refundTime) || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="退款原因">{{
          viewData.refundReason || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="取消时间">
          {{ parseTime(viewData.cancelTime) || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="用户备注" :span="2">{{
          viewData.userRemark || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="管理员备注" :span="2">{{
          viewData.adminRemark || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          parseTime(viewData.createTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{
          parseTime(viewData.updateTime)
        }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 手动分配订单对话框 -->
    <el-dialog
      title="手动分配订单"
      :visible.sync="assignOpen"
      width="600px"
      append-to-body
    >
      <el-form
        ref="assignForm"
        :model="assignForm"
        :rules="assignRules"
        label-width="120px"
      >
        <el-form-item label="用户手机号" prop="userMobile">
          <el-input
            v-model="assignForm.userMobile"
            placeholder="请输入用户手机号"
          />
        </el-form-item>
        <el-form-item label="商品" prop="productId">
          <el-select
            v-model="assignForm.productId"
            placeholder="请选择商品"
            @change="handleAssignProductChange"
          >
            <el-option
              v-for="product in productOptions"
              :key="product.id"
              :label="product.name"
              :value="product.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="商品SKU"
          prop="skuId"
          v-if="assignSelectedProductSkus.length > 0"
        >
          <el-select
            v-model="assignForm.skuId"
            placeholder="请选择商品SKU"
            @change="handleAssignSkuChange"
          >
            <el-option
              v-for="sku in assignSelectedProductSkus"
              :key="sku.id"
              :label="`${sku.skuName} - ¥${formatPrice(sku.salePrice)}`"
              :value="sku.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="价格信息" v-if="assignForm.productId">
          <div style="background: #f5f5f5; padding: 10px; border-radius: 4px">
            <div>原价: ¥{{ formatPrice(assignCurrentOriginalPrice) }}</div>
            <div>售价: ¥{{ formatPrice(assignCurrentSalePrice) }}</div>
          </div>
        </el-form-item>
        <el-form-item label="实付金额(元)" prop="payPriceYuan">
          <el-input-number
            v-model="assignForm.payPriceYuan"
            :min="0"
            :precision="2"
            placeholder="留空使用售价"
          />
        </el-form-item>
        <el-form-item label="优惠金额(元)" prop="discountPriceYuan">
          <el-input-number
            v-model="assignForm.discountPriceYuan"
            :min="0"
            :precision="2"
            placeholder="留空自动计算"
          />
        </el-form-item>
        <el-form-item label="管理员备注" prop="adminRemark">
          <el-input
            v-model="assignForm.adminRemark"
            type="textarea"
            placeholder="请输入管理员备注"
          />
        </el-form-item>
        <el-form-item label="用户备注" prop="userRemark">
          <el-input
            v-model="assignForm.userRemark"
            type="textarea"
            placeholder="请输入用户备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAssignForm">确 定</el-button>
        <el-button @click="cancelAssign">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog
      title="订单退款"
      :visible.sync="refundOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="refundForm"
        :model="refundForm"
        :rules="refundRules"
        label-width="120px"
      >
        <el-form-item label="订单信息">
          <div style="background: #f5f5f5; padding: 10px; border-radius: 4px">
            <div>订单号: {{ refundForm.orderNo }}</div>
            <div>实付金额: ¥{{ formatPrice(refundForm.maxRefundPrice) }}</div>
          </div>
        </el-form-item>
        <el-form-item label="退款金额(元)" prop="refundPriceYuan">
          <el-input-number
            v-model="refundForm.refundPriceYuan"
            :min="0.01"
            :max="refundForm.maxRefundPrice / 100"
            :precision="2"
            placeholder="请输入退款金额"
          />
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input
            v-model="refundForm.refundReason"
            type="textarea"
            placeholder="请输入退款原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRefundForm">确 定</el-button>
        <el-button @click="cancelRefund">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getWbclassCourseProductPage,
  getWbclassCourseProductSkuList,
} from "@/api/wbclass/courseProduct";
import { getWbclassCoursePage } from "@/api/wbclass/wbclassCourse";
import {
  assignCourseOrder,
  cancelCourseOrder,
  getCourseOrderPage,
  refundCourseOrder,
} from "@/api/wbclass/wbclassCourseOrder";

export default {
  name: "WbclassCourseOrder",
  data() {
    return {
      // 遮罩层
      loading: true,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程订单表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 是否显示手动分配弹出层
      assignOpen: false,
      // 是否显示退款弹出层
      refundOpen: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        orderNo: null,
        userId: null,
        courseId: null,
        skuId: null,
        skuName: null,
        status: null,
        payStatus: null,
        createTime: [],
      },
      // 查看详情数据
      viewData: {},
      // 手动分配表单
      assignForm: {},
      // 手动分配表单校验
      assignRules: {
        userMobile: [
          { required: true, message: "用户手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
        productId: [
          { required: true, message: "商品不能为空", trigger: "change" },
        ],
        skuId: [
          { required: true, message: "商品SKU不能为空", trigger: "change" },
        ],
      },
      // 退款表单
      refundForm: {},
      // 退款表单校验
      refundRules: {
        refundPriceYuan: [
          { required: true, message: "退款金额不能为空", trigger: "blur" },
        ],
        refundReason: [
          { required: true, message: "退款原因不能为空", trigger: "blur" },
        ],
      },
      // 订单状态选项
      orderStatusOptions: [
        { value: "1", label: "待支付" },
        { value: "2", label: "已支付" },
        { value: "3", label: "已取消" },
        { value: "4", label: "已退款" },
      ],
      // 支付状态选项
      payStatusOptions: [
        { value: "1", label: "未支付" },
        { value: "2", label: "已支付" },
      ],
      // 课程选项（保留用于其他功能）
      courseOptions: [],
      // 商品选项
      productOptions: [],
      // 分配选中商品的SKU列表
      assignSelectedProductSkus: [],
      // 分配当前原价
      assignCurrentOriginalPrice: 0,
      // 分配当前售价
      assignCurrentSalePrice: 0,
    };
  },
  created() {
    this.getList();
    this.loadCourseOptions();
    this.loadProductOptions();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getCourseOrderPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 加载课程选项 */
    loadCourseOptions() {
      getWbclassCoursePage({ pageNo: 1, pageSize: 1000 }).then((response) => {
        this.courseOptions = response.data.list;
      });
    },
    /** 加载商品选项 */
    loadProductOptions() {
      getWbclassCourseProductPage({ pageNo: 1, pageSize: 1000 }).then(
        (response) => {
          this.productOptions = response.data.list;
        }
      );
    },
    /** 价格格式化 */
    formatPrice(price) {
      if (!price || price === 0) return "0.00";
      return (price / 100).toFixed(2);
    },
    /** 时间格式化 */
    parseTimeFormatter(row, column) {
      return this.parseTime(row[column.property]);
    },
    /** 获取订单状态标签类型 */
    getOrderStatusTagType(status) {
      const types = { 1: "", 2: "success", 3: "info", 4: "warning" };
      return types[status] || "";
    },
    /** 获取订单状态文本 */
    getOrderStatusText(status) {
      const texts = { 1: "待支付", 2: "已支付", 3: "已取消", 4: "已退款" };
      return texts[status] || "未知";
    },
    /** 获取支付状态标签类型 */
    getPayStatusTagType(status) {
      return status === 2 ? "success" : "";
    },
    /** 获取支付状态文本 */
    getPayStatusText(status) {
      const texts = { 1: "未支付", 2: "已支付" };
      return texts[status] || "未知";
    },
    /** 获取终端文本 */
    getTerminalText(terminal) {
      const texts = { 1: "PC", 2: "H5", 3: "小程序", 4: "APP" };
      return texts[terminal] || "未知";
    },
    /** 获取支付渠道文本 */
    getPayChannelText(channel) {
      if (!channel) return "-";
      const texts = { 1: "微信", 2: "支付宝", 99: "线下支付" };
      return texts[channel] || `渠道${channel}`;
    },

    /** 处理分配商品变更 */
    handleAssignProductChange(productId) {
      if (productId) {
        // 获取选中商品的SKU列表
        getWbclassCourseProductSkuList(productId).then((response) => {
          this.assignSelectedProductSkus = response.data || [];
          this.assignForm.skuId = null; // 重置SKU选择
          this.assignCurrentOriginalPrice = 0;
          this.assignCurrentSalePrice = 0;
        });
      } else {
        this.assignSelectedProductSkus = [];
        this.assignForm.skuId = null;
        this.assignCurrentOriginalPrice = 0;
        this.assignCurrentSalePrice = 0;
      }
    },
    /** 处理分配SKU变更 */
    handleAssignSkuChange(skuId) {
      if (skuId) {
        const sku = this.assignSelectedProductSkus.find((s) => s.id === skuId);
        if (sku) {
          this.assignCurrentOriginalPrice = sku.originalPrice;
          this.assignCurrentSalePrice = sku.salePrice;
          // 自动填充价格
          this.assignForm.payPriceYuan = (sku.salePrice / 100).toFixed(2);
          this.assignForm.discountPriceYuan = (
            (sku.originalPrice - sku.salePrice) /
            100
          ).toFixed(2);
        }
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        orderNo: null,
        userId: null,
        courseId: null,
        skuId: null,
        skuName: null,
        status: null,
        payStatus: null,
        createTime: [],
      };
      this.handleQuery();
    },

    /** 查看详情操作 */
    handleView(row) {
      this.viewData = { ...row };
      this.viewOpen = true;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "wbclass/course-order/export-excel",
        {
          ...this.queryParams,
        },
        `course_order_${new Date().getTime()}.xls`
      );
    },

    /** 手动分配订单 */
    handleAssign() {
      this.assignForm = {
        userMobile: "",
        productId: undefined,
        skuId: undefined,
        payPriceYuan: undefined,
        discountPriceYuan: undefined,
        adminRemark: "",
        userRemark: "",
      };
      this.assignSelectedProductSkus = [];
      this.assignCurrentOriginalPrice = 0;
      this.assignCurrentSalePrice = 0;
      this.assignOpen = true;
    },
    /** 取消分配 */
    cancelAssign() {
      this.assignOpen = false;
      this.resetForm("assignForm");
    },
    /** 提交分配表单 */
    submitAssignForm() {
      this.$refs["assignForm"].validate((valid) => {
        if (!valid) return;

        // 转换金额为分
        const submitData = {
          ...this.assignForm,
          payPrice: this.assignForm.payPriceYuan
            ? Math.round(this.assignForm.payPriceYuan * 100)
            : undefined,
          discountPrice: this.assignForm.discountPriceYuan
            ? Math.round(this.assignForm.discountPriceYuan * 100)
            : undefined,
        };

        assignCourseOrder(submitData).then((response) => {
          this.$modal.msgSuccess("订单分配成功");
          this.assignOpen = false;
          this.getList();
        });
      });
    },
    /** 取消订单 */
    handleCancel(row) {
      this.$modal
        .confirm('是否确认取消订单"' + row.orderNo + '"？')
        .then(function () {
          return cancelCourseOrder(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("订单取消成功");
        })
        .catch(() => {});
    },
    /** 退款订单 */
    handleRefund(row) {
      this.refundForm = {
        orderId: row.id,
        orderNo: row.orderNo,
        maxRefundPrice: row.payPrice,
        refundPriceYuan: (row.payPrice / 100).toFixed(2),
        refundReason: "",
      };
      this.refundOpen = true;
    },
    /** 取消退款 */
    cancelRefund() {
      this.refundOpen = false;
      this.resetForm("refundForm");
    },
    /** 提交退款表单 */
    submitRefundForm() {
      this.$refs["refundForm"].validate((valid) => {
        if (!valid) return;

        // 转换金额为分
        const submitData = {
          orderId: this.refundForm.orderId,
          refundPrice: Math.round(this.refundForm.refundPriceYuan * 100),
          refundReason: this.refundForm.refundReason,
        };

        refundCourseOrder(submitData).then((response) => {
          this.$modal.msgSuccess("退款成功");
          this.refundOpen = false;
          this.getList();
        });
      });
    },
  },
};
</script>
