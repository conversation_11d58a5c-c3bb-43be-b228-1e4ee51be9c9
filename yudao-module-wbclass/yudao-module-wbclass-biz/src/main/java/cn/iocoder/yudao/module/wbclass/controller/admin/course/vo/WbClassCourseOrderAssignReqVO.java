package cn.iocoder.yudao.module.wbclass.controller.admin.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

@ApiModel("管理后台 - 练习营课程订单手动分配 Request VO")
@Data
public class WbClassCourseOrderAssignReqVO {

    @ApiModelProperty(value = "用户手机号", required = true, example = "13800138000")
    @NotBlank(message = "用户手机号不能为空")
    private String userMobile;

    @ApiModelProperty(value = "商品ID", required = true, example = "1024")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @ApiModelProperty(value = "商品SKU ID", required = true, example = "1024")
    @NotNull(message = "商品SKU ID不能为空")
    private Long skuId;

    @ApiModelProperty(value = "实付金额，单位：分", example = "14900")
    private Integer payPrice;

    @ApiModelProperty(value = "优惠金额，单位：分", example = "5000")
    private Integer discountPrice;

    @ApiModelProperty(value = "管理员备注", example = "线下收款，手动分配课程")
    private String adminRemark;

    @ApiModelProperty(value = "用户备注", example = "学员急需此课程")
    private String userRemark;

} 