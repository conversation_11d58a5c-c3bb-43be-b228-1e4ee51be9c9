package cn.iocoder.yudao.module.wbclass.controller.admin.course.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 练习营课程订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WbClassCourseOrderPageReqVO extends PageParam {

    @ApiModelProperty(value = "订单号", example = "ORDER20241219001")
    private String orderNo;

    @ApiModelProperty(value = "用户ID", example = "2048")
    private Long userId;

    @ApiModelProperty(value = "课程产品ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "产品ID", example = "1024")
    private Long productId;

    @ApiModelProperty(value = "SKU ID", example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "SKU名称", example = "1-3册")
    private String skuName;

    @ApiModelProperty(value = "订单状态：1-待支付，2-已支付，3-已取消，4-已退款", example = "1")
    private Integer status;

    @ApiModelProperty(value = "支付状态：1-未支付，2-已支付", example = "1")
    private Integer payStatus;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

} 