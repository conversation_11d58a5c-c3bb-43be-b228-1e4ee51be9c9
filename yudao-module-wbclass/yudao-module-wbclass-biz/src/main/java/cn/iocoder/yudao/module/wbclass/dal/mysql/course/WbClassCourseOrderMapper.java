package cn.iocoder.yudao.module.wbclass.dal.mysql.course;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseOrderPageReqVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 练习营课程订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseOrderMapper extends BaseMapperX<WbClassCourseOrderDO> {

    default PageResult<WbClassCourseOrderDO> selectPage(WbClassCourseOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                .likeIfPresent(WbClassCourseOrderDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(WbClassCourseOrderDO::getUserId, reqVO.getUserId())
                .eqIfPresent(WbClassCourseOrderDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(WbClassCourseOrderDO::getProductId, reqVO.getProductId())
                .eqIfPresent(WbClassCourseOrderDO::getSkuId, reqVO.getSkuId())
                .likeIfPresent(WbClassCourseOrderDO::getSkuName, reqVO.getSkuName())
                .eqIfPresent(WbClassCourseOrderDO::getStatus, reqVO.getStatus())
                .eqIfPresent(WbClassCourseOrderDO::getPayStatus, reqVO.getPayStatus())
                .betweenIfPresent(WbClassCourseOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WbClassCourseOrderDO::getId));
    }

    default WbClassCourseOrderDO selectByOrderNo(String orderNo) {
        return selectOne(WbClassCourseOrderDO::getOrderNo, orderNo);
    }

} 