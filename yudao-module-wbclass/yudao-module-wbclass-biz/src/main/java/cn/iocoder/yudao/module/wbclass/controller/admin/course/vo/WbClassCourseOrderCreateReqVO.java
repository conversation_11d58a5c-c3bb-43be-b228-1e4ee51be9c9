package cn.iocoder.yudao.module.wbclass.controller.admin.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("管理后台 - 练习营课程订单创建 Request VO")
@Data
public class WbClassCourseOrderCreateReqVO {

    @ApiModelProperty(value = "课程ID（如果指定了skuId则可为空，系统会从SKU关联的课程中选择）", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "商品SKU ID（可选，如果指定则使用商品SKU逻辑）", example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "用户备注", example = "请尽快安排课程")
    private String userRemark;

} 