package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("用户 App - 练习营课程订单 Response VO")
@Data
public class AppWbClassCourseOrderRespVO {

    @ApiModelProperty(value = "订单ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "订单号", example = "ORDER20241219001")
    private String orderNo;

    @ApiModelProperty(value = "课程产品ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "Java全栈开发练习营")
    private String courseName;

    @ApiModelProperty(value = "产品ID", example = "1024")
    private Long productId;

    @ApiModelProperty(value = "SKU ID", example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "SKU名称", example = "1-3册")
    private String skuName;

    @ApiModelProperty(value = "课程原价，单位：分", example = "19900")
    private Integer originalPrice;

    @ApiModelProperty(value = "优惠金额，单位：分", example = "5000")
    private Integer discountPrice;

    @ApiModelProperty(value = "实付金额，单位：分", example = "14900")
    private Integer payPrice;

    @ApiModelProperty(value = "订单状态：1-待支付，2-已支付，3-已取消，4-已退款", example = "1")
    private Integer status;

    @ApiModelProperty(value = "支付状态：1-未支付，2-已支付", example = "1")
    private Integer payStatus;

    @ApiModelProperty(value = "支付时间", example = "2024-01-01 12:00:00")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "用户备注", example = "请尽快安排课程")
    private String userRemark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

} 