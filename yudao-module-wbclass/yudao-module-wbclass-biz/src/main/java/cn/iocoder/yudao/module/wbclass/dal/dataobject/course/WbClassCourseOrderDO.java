package cn.iocoder.yudao.module.wbclass.dal.dataobject.course;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.wbclass.enums.OrderStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.PayStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 练习营课程订单 DO
 *
 * <AUTHOR>
 */
@TableName("edusys_wbclass_course_order")
@KeySequence("edusys_wbclass_course_order_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WbClassCourseOrderDO extends BaseDO {

    /**
     * 订单ID
     */
    @TableId
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户IP
     */
    private String userIp;

    /**
     * 下单终端：1-PC，2-H5，3-小程序，4-APP
     */
    private Integer terminal;

    /**
     * 课程产品ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 课程原价，单位：分
     */
    private Integer originalPrice;

    /**
     * 优惠金额，单位：分
     */
    private Integer discountPrice;

    /**
     * 实付金额，单位：分
     */
    private Integer payPrice;

    /**
     * 订单状态
     * 
     * 枚举 {@link OrderStatusEnum}
     */
    private Integer status;

    /**
     * 支付状态
     * 
     * 枚举 {@link PayStatusEnum}
     */
    private Integer payStatus;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 支付订单ID
     */
    private Long payOrderId;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 退款状态：1-无需退款，2-退款中，3-已退款
     * 
     * 枚举 {@link cn.iocoder.yudao.module.wbclass.enums.RefundStatusEnum}
     */
    private Integer refundStatus;

    /**
     * 退款金额，单位：分
     */
    private Integer refundPrice;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 取消类型：1-用户取消，2-系统取消，3-超时取消
     */
    private Integer cancelType;

    /**
     * 课程有效期开始时间
     */
    private LocalDateTime validityStartTime;

    /**
     * 课程有效期结束时间
     */
    private LocalDateTime validityEndTime;

    /**
     * 用户备注
     */
    private String userRemark;

    /**
     * 管理员备注
     */
    private String adminRemark;

} 